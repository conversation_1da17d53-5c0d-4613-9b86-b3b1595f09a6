// test/flare_test.dart

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:gameflex_mobile/models/flare_model.dart';
import 'package:gameflex_mobile/services/flare_service.dart';
import 'package:gameflex_mobile/widgets/flare_selection_widget.dart';

void main() {
  group('Flare Tests', () {
    test('FlareService should return all categories', () {
      final service = FlareService();
      final categories = service.getCategories();

      expect(categories.length, equals(6));
      expect(categories, contains(FlareCategory.arrows));
      expect(categories, contains(FlareCategory.bubbles));
      expect(categories, contains(FlareCategory.flexFun));
      expect(categories, contains(FlareCategory.memes));
      expect(categories, contains(FlareCategory.smack));
      expect(categories, contains(FlareCategory.smellies));
    });

    test('FlareService should return items for each category', () {
      final service = FlareService();

      for (final category in FlareCategory.values) {
        final items = service.getFlareForCategory(category);
        expect(items.isNotEmpty, isTrue, reason: 'Category ${category.displayName} should have items');

        for (final item in items) {
          expect(item.category, equals(category));
          expect(item.fileName.isNotEmpty, isTrue);
          expect(item.assetPath.contains(category.folderName), isTrue);
        }
      }
    });

    test('FlareItem should generate correct asset paths', () {
      final item = FlareItem(
        name: 'Test Arrow',
        fileName: 'Arrow 1.png',
        category: FlareCategory.arrows,
      );

      expect(item.assetPath, equals('assets/images/flare/arrows/Arrow 1.png'));
      expect(item.displayName, equals('Arrow 1'));
    });

    test('FlareOverlay should support transformations', () {
      final item = FlareItem(
        name: 'Test',
        fileName: 'test.png',
        category: FlareCategory.arrows,
      );

      final overlay = FlareOverlay(
        item: item,
        x: 0.5,
        y: 0.5,
        scale: 1.0,
        rotation: 0.0,
        id: 'test-id',
      );
      
      final transformed = overlay.copyWith(
        x: 0.7,
        scale: 1.5,
        rotation: 0.5,
      );
      
      expect(transformed.x, equals(0.7));
      expect(transformed.y, equals(0.5)); // unchanged
      expect(transformed.scale, equals(1.5));
      expect(transformed.rotation, equals(0.5));
      expect(transformed.id, equals('test-id')); // unchanged
    });

    testWidgets('FlareSelectionWidget should display categories', (WidgetTester tester) async {
      bool flareSelected = false;
      FlareItem? selectedItem;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: FlareSelectionWidget(
              onFlareSelected: (item) {
                flareSelected = true;
                selectedItem = item;
              },
            ),
          ),
        ),
      );

      // Check if the widget displays the header
      expect(find.text('Add Flare'), findsOneWidget);
      
      // Check if category tabs are displayed
      expect(find.text('Arrows'), findsOneWidget);
      expect(find.text('Bubbles'), findsOneWidget);
      expect(find.text('Flex Fun'), findsOneWidget);
      expect(find.text('Memes'), findsOneWidget);
      expect(find.text('Smack'), findsOneWidget);
      expect(find.text('Smellies'), findsOneWidget);
    });

    test('FlareCategory extensions should work correctly', () {
      expect(FlareCategory.arrows.displayName, equals('Arrows'));
      expect(FlareCategory.flexFun.displayName, equals('Flex Fun'));
      expect(FlareCategory.arrows.folderName, equals('arrows'));
      expect(FlareCategory.flexFun.folderName, equals('flex_fun'));
      expect(FlareCategory.arrows.assetPath, equals('assets/images/flare/arrows/'));
    });

    test('FlareService should find items by filename', () {
      final service = FlareService();

      final foundItem = service.findFlareByFileName('Arrow 1.png');
      expect(foundItem, isNotNull);
      expect(foundItem!.fileName, equals('Arrow 1.png'));
      expect(foundItem.category, equals(FlareCategory.arrows));

      final notFound = service.findFlareByFileName('nonexistent.png');
      expect(notFound, isNull);
    });
  });
}
