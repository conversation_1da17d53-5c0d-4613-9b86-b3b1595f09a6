#!/bin/bash

# GameFlex SAM Backend Startup Script
# This script starts the AWS SAM local environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[GAMEFLEX]${NC} $1"
}

# Load environment variables from .env file
load_env_file() {
    if [ -f ".env" ]; then
        print_status "Loading environment variables from .env file..."
        # Export variables from .env file, ignoring comments and empty lines
        export $(grep -v '^#' .env | grep -v '^$' | xargs)
    else
        print_warning ".env file not found. Using default values."
    fi
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_status "Docker is running"
}

# Check if required ports are available (not needed for sync mode)
check_ports() {
    print_status "Skipping port check - using remote AWS API"
}

# Check if SAM CLI is installed
check_sam_cli() {
    if ! command -v sam &> /dev/null; then
        print_error "AWS SAM CLI is not installed. Please install it first."
        print_status "Installation guide: https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/install-sam-cli.html"
        exit 1
    fi
    print_status "AWS SAM CLI is installed"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."

    # Make scripts executable
    chmod +x scripts/*.sh

    # Run the dependency installation script
    if [ -f "scripts/install-dependencies.sh" ]; then
        ./scripts/install-dependencies.sh
    else
        print_warning "Dependency installation script not found. Skipping."
    fi
}

# Deploy SAM stack to AWS
deploy_sam_stack() {
    print_status "Deploying SAM stack to AWS..."

    # Always deploy without asking
    print_status "Automatically deploying SAM stack..."

    # Check if samconfig.toml exists for guided deploy
    if [ ! -f "samconfig.toml" ]; then
        print_status "No samconfig.toml found. Running guided deploy..."

        # Run guided deploy with default values
        sam deploy --guided \
            --stack-name gameflex-development \
            --parameter-overrides "Environment=development ProjectName=gameflex" \
            --capabilities CAPABILITY_IAM \
            --region us-west-2 \
            --confirm-changeset \
            --resolve-s3
    else
        print_status "Found samconfig.toml. Deploying with existing configuration..."

        # Deploy with existing configuration (allow failure)
        set +e  # Temporarily disable exit on error
        sam deploy \
            --parameter-overrides "Environment=development ProjectName=gameflex" \
            --region us-west-2
        local deploy_exit_code=$?
        set -e  # Re-enable exit on error
    fi
    if [ $deploy_exit_code -eq 0 ]; then
        print_status "✓ SAM stack deployed successfully!"
        return 0
    else
        # Check if it's just "no changes" error (exit code 1)
        if [[ $deploy_exit_code -eq 1 ]]; then
            print_status "✓ SAM stack is up to date - no changes to deploy"
            return 0
        else
            print_warning "⚠️ SAM stack deployment failed! Continuing with existing resources..."
            return 0  # Continue anyway
        fi
    fi
}

# Extract and export AWS resource IDs
export_aws_resources() {
    print_status "Extracting AWS resource IDs from deployed stack..."

    # Get stack name
    STACK_NAME="gameflex-development"

    # Check if stack exists
    if ! aws cloudformation describe-stacks --stack-name $STACK_NAME &>/dev/null; then
        print_warning "⚠️ Stack $STACK_NAME does not exist. Using default values."

        # Set default values for local development
        export USER_POOL_ID="us-west-2_testpool"
        export USER_POOL_CLIENT_ID="testclientid"
        export USERS_TABLE="Users"
        export POSTS_TABLE="Posts"
        export MEDIA_BUCKET="media-bucket"

        return 0
    fi

    # Get User Pool ID
    USER_POOL_ID=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='UserPoolId'].OutputValue" --output text)
    if [ -n "$USER_POOL_ID" ]; then
        print_status "✓ Found User Pool ID: $USER_POOL_ID"
        export USER_POOL_ID
    else
        print_warning "⚠️ User Pool ID not found in stack outputs"
    fi

    # Get User Pool Client ID
    USER_POOL_CLIENT_ID=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='UserPoolClientId'].OutputValue" --output text)
    if [ -n "$USER_POOL_CLIENT_ID" ]; then
        print_status "✓ Found User Pool Client ID: $USER_POOL_CLIENT_ID"
        export USER_POOL_CLIENT_ID
    else
        print_warning "⚠️ User Pool Client ID not found in stack outputs"
    fi

    # Get Users Table name
    USERS_TABLE=$(aws cloudformation describe-stack-resources --stack-name $STACK_NAME --logical-resource-id UsersTable --query "StackResources[0].PhysicalResourceId" --output text)
    if [ -n "$USERS_TABLE" ]; then
        print_status "✓ Found Users Table: $USERS_TABLE"
        export USERS_TABLE
    else
        print_warning "⚠️ Users Table not found in stack resources"
    fi

    # Get Posts Table name
    POSTS_TABLE=$(aws cloudformation describe-stack-resources --stack-name $STACK_NAME --logical-resource-id PostsTable --query "StackResources[0].PhysicalResourceId" --output text)
    if [ -n "$POSTS_TABLE" ]; then
        print_status "✓ Found Posts Table: $POSTS_TABLE"
        export POSTS_TABLE
    else
        print_warning "⚠️ Posts Table not found in stack resources"
    fi

    # Get Media Bucket name
    MEDIA_BUCKET=$(aws cloudformation describe-stack-resources --stack-name $STACK_NAME --logical-resource-id MediaBucket --query "StackResources[0].PhysicalResourceId" --output text)
    if [ -n "$MEDIA_BUCKET" ]; then
        print_status "✓ Found Media Bucket: $MEDIA_BUCKET"
        export MEDIA_BUCKET
    else
        print_warning "⚠️ Media Bucket not found in stack resources"
    fi

    print_status "AWS resource IDs exported as environment variables"
}

# Start SAM sync for continuous deployment
start_sam_sync() {
    print_status "Starting SAM sync for continuous deployment..."

    # Set environment variables for SAM sync
    export AWS_REGION=us-west-2

    print_status "🔄 SAM sync will watch for changes and automatically deploy to AWS"
    print_status "📍 Your API is available at the deployed AWS API Gateway URL"
    print_status "🔄 Press Ctrl+C to stop syncing"

    # Start SAM sync with watch mode (auto-confirm for development)
    echo "Y" | sam sync \
        --stack-name "gameflex-development" \
        --parameter-overrides "Environment=development ProjectName=gameflex" \
        --watch &

    # Save the PID to kill it later
    echo $! > .sam_sync_pid

    print_status "SAM sync started - watching for changes..."
}

# Display service information
display_info() {
    print_header "GameFlex SAM Backend Sync is now running!"
    echo

    # Get the API Gateway URL from the deployed stack
    STACK_NAME="gameflex-development"
    API_URL=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='ApiGatewayUrl'].OutputValue" --output text 2>/dev/null || echo "Not found")

    print_status "Service URLs:"
    echo "  🌐 AWS API Gateway: ${API_URL}"
    echo
    print_status "API Endpoints:"
    echo "  🔐 Authentication: ${API_URL}/auth/signin, /auth/signup, /auth/refresh"
    echo "  📝 Posts: ${API_URL}/posts, /posts/{id}, /posts/{id}/like"
    echo "  📷 Media: ${API_URL}/media/upload, /media/{id}"
    echo "  👤 Users: ${API_URL}/users/profile, /users/{id}, /users/{id}/follow"
    echo "  ❤️  Health: ${API_URL}/health"
    echo
    print_status "AWS Resources:"
    echo "  👥 User Pool ID: ${USER_POOL_ID:-'Not found'}"
    echo "  🔑 User Pool Client ID: ${USER_POOL_CLIENT_ID:-'Not found'}"
    echo "  📊 Users Table: ${USERS_TABLE:-'Not found'}"
    echo "  📝 Posts Table: ${POSTS_TABLE:-'Not found'}"
    echo "  🗂️  Media Bucket: ${MEDIA_BUCKET:-'Not found'}"
    echo
    print_status "Sync Status:"
    echo "  🔄 SAM sync is watching for file changes"
    echo "  📡 Changes will be automatically deployed to AWS"
    echo "  ⚡ No local server - using remote AWS API"
    echo
    print_status "Useful Commands:"
    echo "  🛑 Stop sync: ./stop.sh"
    echo "  🔄 Restart: ./stop.sh && ./start.sh"
    echo
    print_warning "This is a development environment. Do not use in production!"
}

# Cleanup function
cleanup() {
    print_status "Cleaning up..."

    # Kill SAM sync process
    if [ -f .sam_sync_pid ]; then
        kill $(cat .sam_sync_pid) 2>/dev/null || true
        rm .sam_sync_pid
    fi

    print_status "Cleanup completed"
}

# Trap SIGINT and SIGTERM
trap cleanup SIGINT SIGTERM

# Main execution
main() {
    print_header "GameFlex SAM Backend Startup"
    echo

    # Load environment variables from .env file
    load_env_file

    check_docker
    check_ports
    check_sam_cli

    install_dependencies
    deploy_sam_stack
    export_aws_resources
    start_sam_sync
    display_info

    print_status "Startup completed successfully!"

    # Keep the script running to handle signals
    echo
    print_header "🔄 SAM SYNC IS NOW RUNNING IN WATCH MODE 🔄"
    echo
    print_status "✅ Your backend is actively syncing to AWS"
    print_status "📡 Any file changes will be automatically deployed"
    print_status "🌐 API is live at: https://wc19zcdkec.execute-api.us-west-2.amazonaws.com/Dev/"
    print_status "👀 Watching for changes in: backend/src/"
    echo
    print_warning "⚠️  DO NOT CLOSE THIS TERMINAL - SAM sync is running here"
    print_status "🛑 To stop SAM sync: Press Ctrl+C or run ./stop.sh in another terminal"
    echo
    print_status "🚀 Ready for development! Make changes to your backend code and they'll auto-deploy."
    echo

    while true; do
        sleep 1
    done
}

# Run main function
main "$@"
