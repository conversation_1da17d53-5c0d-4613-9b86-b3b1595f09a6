/**
 * Global teardown for Jest tests
 * Runs once after all tests
 */

module.exports = async () => {
  console.log('🧹 Cleaning up after test suite...');

  // Force garbage collection if available
  if (global.gc) {
    global.gc();
  }

  // Give time for any pending HTTP connections to close
  await new Promise(resolve => setTimeout(resolve, 100));

  console.log('✅ Global test teardown completed');
};
