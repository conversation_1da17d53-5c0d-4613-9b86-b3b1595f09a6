/**
 * Test data generators and mock data for GameFlex SAM Backend tests
 */

const { v4: uuidv4 } = require('uuid');

class TestDataGenerator {
  static createUser(overrides = {}) {
    const now = new Date().toISOString();
    const username = `testuser_${Math.random().toString(36).substring(7)}`;

    return {
      id: uuidv4(),
      cognito_user_id: `cognito_${uuidv4()}`,
      email: `${username}@test.gameflex.com`,
      username,
      firstName: 'Test',
      lastName: 'User',
      created_at: now,
      updated_at: now,
      ...overrides
    };
  }

  static createPost(userId, overrides = {}) {
    const now = new Date().toISOString();

    return {
      id: uuidv4(),
      title: `Test Post ${Math.random().toString(36).substring(7)}`,
      content: 'This is a test post content',
      userId: userId || uuidv4(),
      author_id: userId || uuidv4(),
      mediaUrl: null,
      media_id: null,
      likes: 0,
      comments: 0,
      status: 'published',
      active: true,
      created_at: now,
      updated_at: now,
      ...overrides
    };
  }

  static createMedia(userId, overrides = {}) {
    const now = new Date().toISOString();
    const filename = `test_image_${Math.random().toString(36).substring(7)}.jpg`;

    return {
      id: uuidv4(),
      fileName: filename,
      fileType: 'image/jpeg',
      fileSize: 1024000,
      mediaType: 'image',
      userId: userId || uuidv4(),
      s3Key: `media/${filename}`,
      bucketName: 'gameflex-media-test',
      url: `http://localhost:4566/gameflex-media-test/media/${filename}`,
      status: 'uploaded',
      created_at: now,
      updated_at: now,
      ...overrides
    };
  }

  static createUserProfile(userId, overrides = {}) {
    const now = new Date().toISOString();

    return {
      user_id: userId || uuidv4(),
      bio: 'Test user bio',
      location: 'Test City',
      website: 'https://test.example.com',
      avatarUrl: null,
      followersCount: 0,
      followingCount: 0,
      postsCount: 0,
      created_at: now,
      updated_at: now,
      ...overrides
    };
  }

  static createAuthRequest(overrides = {}) {
    return {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      username: 'testuser',
      firstName: 'Test',
      lastName: 'User',
      ...overrides
    };
  }

  static createAPIGatewayEvent(overrides = {}) {
    return {
      httpMethod: 'GET',
      path: '/test',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      queryStringParameters: null,
      pathParameters: null,
      body: null,
      isBase64Encoded: false,
      requestContext: {
        requestId: uuidv4(),
        stage: 'test',
        resourcePath: '/test',
        httpMethod: 'GET',
        requestTime: new Date().toISOString(),
        protocol: 'HTTP/1.1',
        resourceId: 'test-resource',
        accountId: '************',
        apiId: 'test-api-id',
        identity: {
          sourceIp: '127.0.0.1',
          userAgent: 'test-agent'
        }
      },
      ...overrides
    };
  }

  static createLambdaContext() {
    return {
      callbackWaitsForEmptyEventLoop: false,
      functionName: 'test-function',
      functionVersion: '$LATEST',
      invokedFunctionArn: 'arn:aws:lambda:us-east-1:************:function:test-function',
      memoryLimitInMB: '256',
      awsRequestId: uuidv4(),
      logGroupName: '/aws/lambda/test-function',
      logStreamName: '2024/01/01/[$LATEST]test',
      getRemainingTimeInMillis: () => 30000,
      done: () => { },
      fail: () => { },
      succeed: () => { }
    };
  }

  static createCognitoUser(overrides = {}) {
    return {
      User: {
        Username: '<EMAIL>',
        Attributes: [
          { Name: 'email', Value: '<EMAIL>' },
          { Name: 'email_verified', Value: 'true' },
          { Name: 'given_name', Value: 'Test' },
          { Name: 'family_name', Value: 'User' }
        ],
        UserCreateDate: new Date(),
        UserLastModifiedDate: new Date(),
        Enabled: true,
        UserStatus: 'CONFIRMED'
      },
      ...overrides
    };
  }

  static createCognitoAuthResult(overrides = {}) {
    return {
      AuthenticationResult: {
        AccessToken: 'mock-access-token',
        RefreshToken: 'mock-refresh-token',
        IdToken: 'mock-id-token',
        TokenType: 'Bearer',
        ExpiresIn: 3600
      },
      ...overrides
    };
  }
}

// Pre-defined test users for consistent testing
const TEST_USERS = {
  VALID_USER: TestDataGenerator.createUser({
    email: '<EMAIL>',
    username: 'devuser',
    firstName: 'Dev',
    lastName: 'User'
  }),

  ADMIN_USER: TestDataGenerator.createUser({
    email: '<EMAIL>',
    username: 'admin',
    firstName: 'Admin',
    lastName: 'User'
  }),

  INACTIVE_USER: TestDataGenerator.createUser({
    email: '<EMAIL>',
    username: 'inactive',
    firstName: 'Inactive',
    lastName: 'User'
  })
};

const TEST_CREDENTIALS = {
  VALID: {
    email: '<EMAIL>',
    password: 'DevPassword123!'
  },

  INVALID_EMAIL: {
    email: '<EMAIL>',
    password: 'SomePassword123!'
  },

  INVALID_PASSWORD: {
    email: '<EMAIL>',
    password: 'WrongPassword123!'
  }
};

module.exports = {
  TestDataGenerator,
  TEST_USERS,
  TEST_CREDENTIALS
};
