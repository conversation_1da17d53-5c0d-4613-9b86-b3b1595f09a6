/**
 * Simple health check integration test
 * Tests that the backend is running and responding to requests
 */

const axios = require('axios');

// Create axios instance with proper cleanup
const httpClient = axios.create({
  timeout: 10000,
  headers: {
    'Connection': 'close'
  }
});

// Base URL for the API - use remote endpoint for testing
const API_URL = process.env.API_URL_REMOTE || 'http://localhost:3000';

// Helper to check if backend is running
const checkBackendRunning = async () => {
  try {
    await httpClient.get(`${API_URL}/health`);
    return true;
  } catch (error) {
    return false;
  }
};

// Skip all tests if backend is not running
beforeAll(async () => {
  const isRunning = await checkBackendRunning();
  if (!isRunning) {
    console.error(`\n⚠️ Backend is not running at ${API_URL}`);
    console.error('Please ensure the backend is deployed and accessible\n');
    // This will cause <PERSON><PERSON> to skip all tests
    throw new Error('Backend not running');
  }
  console.log('\n✅ Backend is running, proceeding with tests\n');
});

describe('Health Check API', () => {
  it('should return 200 OK status', async () => {
    const response = await httpClient.get(`${API_URL}/health`);
    expect(response.status).toBe(200);
  });

  it('should return healthy status in response body', async () => {
    const response = await httpClient.get(`${API_URL}/health`);
    expect(response.data).toBeDefined();
    expect(response.data.status).toBe('healthy');
  });

  it('should include service status information', async () => {
    const response = await httpClient.get(`${API_URL}/health`);
    expect(response.data.services).toBeDefined();
    expect(response.data.services.api).toBeDefined();
    // Database might be healthy or unhealthy depending on configuration
    expect(response.data.services.database).toBeDefined();
  });

  it('should include CORS headers', async () => {
    const response = await httpClient.get(`${API_URL}/health`);
    expect(response.headers['access-control-allow-origin']).toBeDefined();
  });

  it('should include version information', async () => {
    const response = await httpClient.get(`${API_URL}/health`);
    expect(response.data.version).toBeDefined();
  });

  it('should include environment information', async () => {
    const response = await httpClient.get(`${API_URL}/health`);
    expect(response.data.environment).toBeDefined();
  });
});
