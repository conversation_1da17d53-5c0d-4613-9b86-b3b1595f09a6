/**
 * Integration tests for Multi-Step Post Creation workflow
 * Tests the complete flow: draft -> upload media -> attach media -> publish
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

// Test configuration
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'TestPassword123!';

describe('Multi-Step Post Creation Integration Tests', () => {
  let accessToken;
  let testUserId;

  beforeAll(async () => {
    // Skip if no API URL configured
    if (!process.env.API_BASE_URL && !process.env.CI) {
      console.log('Skipping integration tests - API_BASE_URL not configured');
      return;
    }

    // Authenticate test user
    try {
      const authResponse = await axios.post(`${API_BASE_URL}/auth/signin`, {
        email: TEST_USER_EMAIL,
        password: TEST_USER_PASSWORD
      });

      accessToken = authResponse.data.accessToken;
      testUserId = authResponse.data.user.id;
      console.log('✅ Authentication successful');
    } catch (error) {
      console.log('⚠️ Authentication failed, skipping tests:', error.message);
      return;
    }
  });

  describe('Complete Multi-Step Workflow', () => {
    let draftPostId;
    let mediaId;

    it('should create a draft post', async () => {
      if (!accessToken) {
        console.log('Skipping test - no access token');
        return;
      }

      const postData = {
        title: 'Integration Test Post',
        content: 'This is a test post created during integration testing'
      };

      const response = await axios.post(
        `${API_BASE_URL}/posts/draft`,
        postData,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      expect(response.status).toBe(201);
      expect(response.data.message).toBe('Draft post created successfully');
      expect(response.data.post).toBeDefined();
      expect(response.data.post.status).toBe('draft');
      expect(response.data.post.active).toBe(false);
      expect(response.data.post.content).toBe(postData.content);
      expect(response.data.post.title).toBe(postData.title);

      draftPostId = response.data.post.id;
      console.log('✅ Draft post created:', draftPostId);
    });

    it('should request upload URL for media', async () => {
      if (!accessToken || !draftPostId) {
        console.log('Skipping test - missing prerequisites');
        return;
      }

      const uploadRequest = {
        fileName: 'test-image.jpg',
        fileType: 'image/jpeg',
        fileSize: 1024,
        mediaType: 'image'
      };

      const response = await axios.post(
        `${API_BASE_URL}/media/upload`,
        uploadRequest,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      expect(response.status).toBe(200);
      expect(response.data.message).toBe('Upload URL generated successfully');
      expect(response.data.mediaId).toBeDefined();
      expect(response.data.uploadUrl).toBeDefined();
      expect(response.data.media).toBeDefined();
      expect(response.data.media.status).toBe('pending');

      mediaId = response.data.mediaId;
      console.log('✅ Upload URL generated for media:', mediaId);
    });

    it('should simulate media upload completion', async () => {
      if (!accessToken || !mediaId) {
        console.log('Skipping test - missing prerequisites');
        return;
      }

      // Simulate successful upload by updating media status
      const response = await axios.put(
        `${API_BASE_URL}/media/${mediaId}`,
        { status: 'uploaded' },
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      expect(response.status).toBe(200);
      expect(response.data.message).toBe('Media status updated successfully');
      expect(response.data.media.status).toBe('uploaded');

      console.log('✅ Media upload completed');
    });

    it('should attach media to draft post', async () => {
      if (!accessToken || !draftPostId || !mediaId) {
        console.log('Skipping test - missing prerequisites');
        return;
      }

      const response = await axios.put(
        `${API_BASE_URL}/posts/${draftPostId}/media`,
        { media_id: mediaId },
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      expect(response.status).toBe(200);
      expect(response.data.message).toBe('Media attached to post successfully');
      expect(response.data.post).toBeDefined();
      expect(response.data.post.media_id).toBe(mediaId);
      expect(response.data.post.status).toBe('uploading_media');

      console.log('✅ Media attached to post');
    });

    it('should publish the post', async () => {
      if (!accessToken || !draftPostId) {
        console.log('Skipping test - missing prerequisites');
        return;
      }

      const response = await axios.put(
        `${API_BASE_URL}/posts/${draftPostId}/publish`,
        {},
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      expect(response.status).toBe(200);
      expect(response.data.message).toBe('Post published successfully');
      expect(response.data.post).toBeDefined();
      expect(response.data.post.status).toBe('published');
      expect(response.data.post.active).toBe(true);

      console.log('✅ Post published successfully');
    });

    it('should show published post in feed', async () => {
      if (!accessToken || !draftPostId) {
        console.log('Skipping test - missing prerequisites');
        return;
      }

      const response = await axios.get(
        `${API_BASE_URL}/posts`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        }
      );

      expect(response.status).toBe(200);
      expect(response.data.posts).toBeDefined();
      
      // Find our published post in the feed
      const publishedPost = response.data.posts.find(post => post.id === draftPostId);
      expect(publishedPost).toBeDefined();
      expect(publishedPost.status).toBe('published');
      expect(publishedPost.active).toBe(true);

      console.log('✅ Published post appears in feed');
    });

    afterAll(async () => {
      // Clean up: delete the test post
      if (accessToken && draftPostId) {
        try {
          await axios.delete(
            `${API_BASE_URL}/posts/${draftPostId}`,
            {
              headers: {
                'Authorization': `Bearer ${accessToken}`
              }
            }
          );
          console.log('✅ Test post cleaned up');
        } catch (error) {
          console.log('⚠️ Failed to clean up test post:', error.message);
        }
      }

      // Clean up: delete the test media
      if (accessToken && mediaId) {
        try {
          await axios.delete(
            `${API_BASE_URL}/media/${mediaId}`,
            {
              headers: {
                'Authorization': `Bearer ${accessToken}`
              }
            }
          );
          console.log('✅ Test media cleaned up');
        } catch (error) {
          console.log('⚠️ Failed to clean up test media:', error.message);
        }
      }
    });
  });

  describe('Error Handling', () => {
    it('should reject publishing post with incomplete media upload', async () => {
      if (!accessToken) {
        console.log('Skipping test - no access token');
        return;
      }

      // Create draft post
      const draftResponse = await axios.post(
        `${API_BASE_URL}/posts/draft`,
        { content: 'Test post with incomplete media' },
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const draftPostId = draftResponse.data.post.id;

      // Request upload URL but don't complete upload
      const uploadResponse = await axios.post(
        `${API_BASE_URL}/media/upload`,
        {
          fileName: 'incomplete-test.jpg',
          fileType: 'image/jpeg',
          fileSize: 1024,
          mediaType: 'image'
        },
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const mediaId = uploadResponse.data.mediaId;

      // Try to attach media that hasn't been uploaded
      try {
        await axios.put(
          `${API_BASE_URL}/posts/${draftPostId}/media`,
          { media_id: mediaId },
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );
        fail('Should have rejected incomplete media upload');
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.error).toBe('Media upload not completed');
      }

      // Clean up
      await axios.delete(`${API_BASE_URL}/posts/${draftPostId}`, {
        headers: { 'Authorization': `Bearer ${accessToken}` }
      });
      await axios.delete(`${API_BASE_URL}/media/${mediaId}`, {
        headers: { 'Authorization': `Bearer ${accessToken}` }
      });

      console.log('✅ Correctly rejected incomplete media upload');
    });
  });
});
