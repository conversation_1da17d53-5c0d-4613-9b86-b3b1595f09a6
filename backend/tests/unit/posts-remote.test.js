/**
 * Remote API tests for Posts Lambda function - Multi-Step Post Creation
 */

const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

// Test configuration
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';
const TEST_USER_EMAIL = '<EMAIL>';
const TEST_USER_PASSWORD = 'TestPassword123!';

describe('Posts Remote API Tests', () => {
  let accessToken;
  let testUserId;

  beforeAll(async () => {
    // Skip if no API URL configured
    if (!process.env.API_BASE_URL && !process.env.CI) {
      console.log('Skipping tests - API_BASE_URL not configured');
      return;
    }

    // Authenticate test user
    try {
      const authResponse = await axios.post(`${API_BASE_URL}/auth/signin`, {
        email: TEST_USER_EMAIL,
        password: TEST_USER_PASSWORD
      });

      accessToken = authResponse.data.accessToken;
      testUserId = authResponse.data.user.id;
      console.log('✅ Authentication successful for remote API tests');
    } catch (error) {
      console.log('⚠️ Authentication failed, skipping tests:', error.message);
      return;
    }
  });

  describe('Basic Posts API', () => {
    it('should return all posts successfully', async () => {
      if (!accessToken) {
        console.log('Skipping test - no access token');
        return;
      }

      const response = await axios.get(`${API_BASE_URL}/posts`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      expect(response.status).toBe(200);
      expect(response.data.posts).toBeDefined();
      expect(Array.isArray(response.data.posts)).toBe(true);
    });

    it('should create a regular post successfully', async () => {
      if (!accessToken) {
        console.log('Skipping test - no access token');
        return;
      }

      const postData = {
        content: 'Test post from remote API unit test'
      };

      const response = await axios.post(`${API_BASE_URL}/posts`, postData, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      expect(response.status).toBe(201);
      expect(response.data.message).toBe('Post created successfully');
      expect(response.data.post).toBeDefined();
      expect(response.data.post.content).toBe(postData.content);
      expect(response.data.post.status).toBe('published');
      expect(response.data.post.active).toBe(true);

      // Clean up
      await axios.delete(`${API_BASE_URL}/posts/${response.data.post.id}`, {
        headers: { 'Authorization': `Bearer ${accessToken}` }
      });
    });
  });

  describe('Multi-Step Post Creation', () => {
    describe('POST /posts/draft', () => {
      it('should create draft post successfully', async () => {
        if (!accessToken) {
          console.log('Skipping test - no access token');
          return;
        }

        const draftData = {
          title: 'Test Draft',
          content: 'This is a draft post content'
        };

        const response = await axios.post(`${API_BASE_URL}/posts/draft`, draftData, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        });

        expect(response.status).toBe(201);
        expect(response.data.message).toBe('Draft post created successfully');
        expect(response.data.post).toBeDefined();
        expect(response.data.post.status).toBe('draft');
        expect(response.data.post.active).toBe(false);
        expect(response.data.post.content).toBe(draftData.content);
        expect(response.data.post.title).toBe(draftData.title);

        // Clean up
        await axios.delete(`${API_BASE_URL}/posts/${response.data.post.id}`, {
          headers: { 'Authorization': `Bearer ${accessToken}` }
        });
      });

      it('should require content for draft post', async () => {
        if (!accessToken) {
          console.log('Skipping test - no access token');
          return;
        }

        try {
          await axios.post(`${API_BASE_URL}/posts/draft`, { title: 'Test' }, {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          });
          fail('Should have thrown an error');
        } catch (error) {
          expect(error.response.status).toBe(400);
          expect(error.response.data.error).toBe('Content is required');
        }
      });

      it('should require authentication for draft post', async () => {
        try {
          await axios.post(`${API_BASE_URL}/posts/draft`, { content: 'Test content' }, {
            headers: {
              'Content-Type': 'application/json'
            }
          });
          fail('Should have thrown an error');
        } catch (error) {
          expect(error.response.status).toBe(401);
        }
      });
    });

    describe('PUT /posts/{id}/media', () => {
      let draftPostId;

      beforeEach(async () => {
        if (!accessToken) return;

        // Create a draft post for testing
        const response = await axios.post(`${API_BASE_URL}/posts/draft`, {
          content: 'Test post for media attachment'
        }, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        });
        draftPostId = response.data.post.id;
      });

      afterEach(async () => {
        if (!accessToken || !draftPostId) return;

        // Clean up
        try {
          await axios.delete(`${API_BASE_URL}/posts/${draftPostId}`, {
            headers: { 'Authorization': `Bearer ${accessToken}` }
          });
        } catch (error) {
          // Ignore cleanup errors
        }
      });

      it('should require media_id', async () => {
        if (!accessToken || !draftPostId) {
          console.log('Skipping test - missing prerequisites');
          return;
        }

        try {
          await axios.put(`${API_BASE_URL}/posts/${draftPostId}/media`, {}, {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          });
          fail('Should have thrown an error');
        } catch (error) {
          expect(error.response.status).toBe(400);
          expect(error.response.data.error).toBe('media_id is required');
        }
      });

      it('should verify media exists', async () => {
        if (!accessToken || !draftPostId) {
          console.log('Skipping test - missing prerequisites');
          return;
        }

        try {
          await axios.put(`${API_BASE_URL}/posts/${draftPostId}/media`, {
            media_id: 'invalid-media-id'
          }, {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          });
          fail('Should have thrown an error');
        } catch (error) {
          expect(error.response.status).toBe(400);
          expect(error.response.data.error).toBe('Invalid media_id: media not found');
        }
      });
    });

    describe('PUT /posts/{id}/publish', () => {
      let draftPostId;

      beforeEach(async () => {
        if (!accessToken) return;

        // Create a draft post for testing
        const response = await axios.post(`${API_BASE_URL}/posts/draft`, {
          content: 'Test post for publishing'
        }, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        });
        draftPostId = response.data.post.id;
      });

      afterEach(async () => {
        if (!accessToken || !draftPostId) return;

        // Clean up
        try {
          await axios.delete(`${API_BASE_URL}/posts/${draftPostId}`, {
            headers: { 'Authorization': `Bearer ${accessToken}` }
          });
        } catch (error) {
          // Ignore cleanup errors
        }
      });

      it('should publish draft post successfully', async () => {
        if (!accessToken || !draftPostId) {
          console.log('Skipping test - missing prerequisites');
          return;
        }

        const response = await axios.put(`${API_BASE_URL}/posts/${draftPostId}/publish`, {}, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        });

        expect(response.status).toBe(200);
        expect(response.data.message).toBe('Post published successfully');
        expect(response.data.post).toBeDefined();
        expect(response.data.post.status).toBe('published');
        expect(response.data.post.active).toBe(true);
      });

      it('should require authentication', async () => {
        if (!draftPostId) {
          console.log('Skipping test - no draft post');
          return;
        }

        try {
          await axios.put(`${API_BASE_URL}/posts/${draftPostId}/publish`, {}, {
            headers: {
              'Content-Type': 'application/json'
            }
          });
          fail('Should have thrown an error');
        } catch (error) {
          expect(error.response.status).toBe(401);
        }
      });

      it('should verify post exists', async () => {
        if (!accessToken) {
          console.log('Skipping test - no access token');
          return;
        }

        try {
          await axios.put(`${API_BASE_URL}/posts/invalid-id/publish`, {}, {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json'
            }
          });
          fail('Should have thrown an error');
        } catch (error) {
          expect(error.response.status).toBe(404);
          expect(error.response.data.error).toBe('Post not found');
        }
      });
    });
  });
});
