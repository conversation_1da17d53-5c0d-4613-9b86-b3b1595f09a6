#!/bin/bash

# GameFlex SAM Logs Viewer (Simple Version)
# This script uses SAM CLI to tail logs for Lambda functions

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[GAMEFLEX]${NC} $1"
}

# Check if SAM CLI is installed
check_sam_cli() {
    if ! command -v sam &> /dev/null; then
        print_error "AWS SAM CLI is not installed. Please install it first."
        exit 1
    fi
    print_status "AWS SAM CLI is installed"
}

# Display usage menu
show_menu() {
    echo
    print_header "Select Lambda Function to Monitor:"
    echo
    echo "1) AuthFunction (Authentication)"
    echo "2) PostsFunction (Posts API)"
    echo "3) UsersFunction (Users API)"
    echo "4) MediaFunction (Media API)"
    echo "5) HealthFunction (Health Check)"
    echo "6) AuthorizerFunction (API Gateway Authorizer)"
    echo "7) All Functions (Multiple terminals)"
    echo "8) Exit"
    echo
}

# Start SAM logs for a specific function
start_sam_logs() {
    local function_name=$1
    local stack_name="gameflex-development"
    
    print_status "Starting SAM logs for $function_name..."
    print_status "Press Ctrl+C to stop"
    echo
    
    # Use SAM CLI to tail logs
    sam logs \
        --name "$function_name" \
        --stack-name "$stack_name" \
        --tail \
        --include-traces
}

# Start logs for all functions (opens multiple terminals)
start_all_logs() {
    local functions=("AuthFunction" "PostsFunction" "UsersFunction" "MediaFunction" "HealthFunction" "AuthorizerFunction")
    
    print_status "Opening separate terminals for each Lambda function..."
    
    for func in "${functions[@]}"; do
        # Try different terminal emulators
        if command -v gnome-terminal &> /dev/null; then
            gnome-terminal --title="$func Logs" -- bash -c "cd $(pwd) && sam logs --name $func --stack-name gameflex-development --tail --include-traces; read -p 'Press Enter to close...'"
        elif command -v xterm &> /dev/null; then
            xterm -title "$func Logs" -e "cd $(pwd) && sam logs --name $func --stack-name gameflex-development --tail --include-traces; read -p 'Press Enter to close...'" &
        elif command -v konsole &> /dev/null; then
            konsole --title "$func Logs" -e bash -c "cd $(pwd) && sam logs --name $func --stack-name gameflex-development --tail --include-traces; read -p 'Press Enter to close...'" &
        else
            print_warning "No supported terminal emulator found. Please run manually:"
            print_status "sam logs --name $func --stack-name gameflex-development --tail --include-traces"
        fi
        sleep 0.5
    done
    
    print_status "Opened terminals for all Lambda functions"
    print_status "Each function has its own terminal window"
}

# Cleanup function
cleanup() {
    print_status "Stopping SAM logs..."
    exit 0
}

# Trap SIGINT and SIGTERM
trap cleanup SIGINT SIGTERM

# Main execution
main() {
    print_header "GameFlex SAM Logs Viewer (Simple)"
    echo
    
    check_sam_cli
    
    while true; do
        show_menu
        read -p "Enter your choice (1-8): " choice
        
        case $choice in
            1)
                start_sam_logs "AuthFunction"
                ;;
            2)
                start_sam_logs "PostsFunction"
                ;;
            3)
                start_sam_logs "UsersFunction"
                ;;
            4)
                start_sam_logs "MediaFunction"
                ;;
            5)
                start_sam_logs "HealthFunction"
                ;;
            6)
                start_sam_logs "AuthorizerFunction"
                ;;
            7)
                start_all_logs
                break
                ;;
            8)
                print_status "Goodbye!"
                exit 0
                ;;
            *)
                print_error "Invalid choice. Please enter 1-8."
                ;;
        esac
    done
}

# Run main function
main "$@"
