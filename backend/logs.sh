#!/bin/bash

# GameFlex SAM Lambda Logs Viewer
# This script tails Cloud<PERSON>atch logs for all Lambda functions in real-time

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[GAMEFLEX]${NC} $1"
}

print_lambda() {
    local lambda_name=$1
    local message=$2
    case $lambda_name in
        *Auth*)
            echo -e "${CYAN}[AUTH]${NC} $message"
            ;;
        *Posts*)
            echo -e "${MAGENTA}[POSTS]${NC} $message"
            ;;
        *Users*)
            echo -e "${YELLOW}[USERS]${NC} $message"
            ;;
        *Media*)
            echo -e "${GREEN}[MEDIA]${NC} $message"
            ;;
        *Health*)
            echo -e "${BLUE}[HEALTH]${NC} $message"
            ;;
        *Authorizer*)
            echo -e "${RED}[AUTHORIZER]${NC} $message"
            ;;
        *)
            echo -e "${NC}[LAMBDA]${NC} $message"
            ;;
    esac
}

# Check if AWS CLI is configured
check_aws_cli() {
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS CLI not configured. Please run 'aws configure' first."
        exit 1
    fi
    print_status "AWS CLI is configured"
}

# Check if SAM CLI is installed
check_sam_cli() {
    if ! command -v sam &> /dev/null; then
        print_error "AWS SAM CLI is not installed. Please install it first."
        exit 1
    fi
    print_status "AWS SAM CLI is installed"
}

# Get Lambda function names from the deployed stack
get_lambda_functions() {
    print_status "Getting Lambda function names from stack..."
    
    local stack_name="gameflex-development"
    
    # Check if stack exists
    if ! aws cloudformation describe-stacks --stack-name $stack_name &>/dev/null; then
        print_error "Stack $stack_name does not exist. Please deploy first."
        exit 1
    fi
    
    # Get all Lambda function names from the stack
    LAMBDA_FUNCTIONS=$(aws cloudformation describe-stack-resources \
        --stack-name $stack_name \
        --resource-type "AWS::Lambda::Function" \
        --query "StackResources[].PhysicalResourceId" \
        --output text)
    
    if [ -z "$LAMBDA_FUNCTIONS" ]; then
        print_error "No Lambda functions found in stack $stack_name"
        exit 1
    fi
    
    print_status "Found Lambda functions:"
    for func in $LAMBDA_FUNCTIONS; do
        print_status "  - $func"
    done
}

# Start tailing logs for a specific Lambda function
tail_lambda_logs() {
    local function_name=$1
    local log_group="/aws/lambda/$function_name"
    
    print_status "Starting log tail for $function_name..."
    
    # Use AWS CLI to tail logs (runs in background)
    aws logs tail $log_group \
        --follow \
        --format short \
        --filter-pattern "" \
        --since 1m 2>/dev/null | while read line; do
        if [ ! -z "$line" ]; then
            print_lambda "$function_name" "$line"
        fi
    done &
    
    # Store the PID for cleanup
    echo $! >> .lambda_log_pids
}

# Start tailing logs for all Lambda functions
start_log_tailing() {
    print_status "Starting log tailing for all Lambda functions..."
    
    # Remove old PID file
    rm -f .lambda_log_pids
    
    # Start tailing each function's logs
    for func in $LAMBDA_FUNCTIONS; do
        tail_lambda_logs "$func"
        sleep 0.5  # Small delay between starting each tail
    done
    
    print_status "Log tailing started for all functions"
}

# Display usage information
display_usage() {
    print_header "Lambda Logs Viewer - Real-time CloudWatch Log Streaming"
    echo
    print_status "This script will display real-time logs from all your Lambda functions"
    print_status "Each function's logs are color-coded for easy identification:"
    echo "  ${CYAN}AUTH${NC}       - Authentication function logs"
    echo "  ${MAGENTA}POSTS${NC}      - Posts function logs"
    echo "  ${YELLOW}USERS${NC}      - Users function logs"
    echo "  ${GREEN}MEDIA${NC}      - Media function logs"
    echo "  ${BLUE}HEALTH${NC}     - Health function logs"
    echo "  ${RED}AUTHORIZER${NC} - API Gateway authorizer logs"
    echo
    print_status "Logs will appear as your Lambda functions are invoked"
    print_status "Press Ctrl+C to stop log tailing"
    echo
}

# Cleanup function
cleanup() {
    print_status "Stopping log tailing..."
    
    # Kill all log tail processes
    if [ -f .lambda_log_pids ]; then
        while read pid; do
            kill $pid 2>/dev/null || true
        done < .lambda_log_pids
        rm .lambda_log_pids
    fi
    
    # Also kill any remaining aws logs tail processes
    pkill -f "aws logs tail" 2>/dev/null || true
    
    print_status "Log tailing stopped"
    exit 0
}

# Trap SIGINT and SIGTERM
trap cleanup SIGINT SIGTERM

# Main execution
main() {
    print_header "GameFlex Lambda Logs Viewer"
    echo
    
    check_aws_cli
    check_sam_cli
    get_lambda_functions
    display_usage
    start_log_tailing
    
    print_header "🔍 LAMBDA LOGS ARE NOW STREAMING 🔍"
    echo
    print_status "✅ Monitoring CloudWatch logs for all Lambda functions"
    print_status "📡 Real-time log streaming is active"
    print_status "🎯 Trigger your API endpoints to see logs appear"
    echo
    print_warning "⚠️  DO NOT CLOSE THIS TERMINAL - Log streaming is running here"
    print_status "🛑 To stop log streaming: Press Ctrl+C"
    echo
    
    # Keep the script running
    while true; do
        sleep 1
    done
}

# Run main function
main "$@"
