#!/bin/bash

# GameFlex Data Seeding Wrapper Script
# This script runs the comprehensive AWS data seeding for the SAM backend
#
# Usage: ./seed-data.sh [-f|--force]
#   -f, --force    Delete all existing data before seeding (fresh start)

set -e

# Parse command line arguments
FORCE_FLAG=""
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--force)
            FORCE_FLAG="-f"
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [-f|--force]"
            echo "  -f, --force    Delete all existing data before seeding (fresh start)"
            echo "  -h, --help     Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use -h or --help for usage information"
            exit 1
            ;;
    esac
done

if [ -n "$FORCE_FLAG" ]; then
    echo "🌱 GameFlex Data Seeding (FORCE MODE - Fresh Data)"
else
    echo "🌱 GameFlex Data Seeding"
fi
echo "========================"
echo

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Check if we're in the backend directory
if [ ! -f "$SCRIPT_DIR/template.yaml" ]; then
    echo "❌ Error: This script must be run from the backend directory"
    echo "   Current directory: $SCRIPT_DIR"
    echo "   Expected to find: template.yaml"
    exit 1
fi

# Check if AWS credentials are configured
echo "🔍 Checking AWS credentials..."
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "❌ Error: AWS credentials not configured"
    echo "   Please configure your AWS credentials:"
    echo "   aws configure"
    echo "   Or set environment variables:"
    echo "   export AWS_ACCESS_KEY_ID=your_key"
    echo "   export AWS_SECRET_ACCESS_KEY=your_secret"
    echo "   export AWS_DEFAULT_REGION=us-east-1"
    exit 1
fi

# Check if SAM application is deployed
echo "🔍 Checking if SAM application is deployed..."
if ! aws dynamodb describe-table --table-name "${PROJECT_NAME:-gameflex}-${ENVIRONMENT:-development}-Users" > /dev/null 2>&1; then
    echo "⚠️  Warning: SAM application doesn't seem to be deployed"
    echo "   Make sure to deploy your SAM application first:"
    echo "   sam build && sam deploy"
    echo
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Aborted."
        exit 1
    fi
fi

echo "🚀 Starting data seeding process..."
echo

# Set environment variables for the seeding script
export ENVIRONMENT=${ENVIRONMENT:-development}
export PROJECT_NAME=${PROJECT_NAME:-gameflex}
export AWS_REGION=${AWS_REGION:-us-east-1}

# Run the simplified seeding script
"$SCRIPT_DIR/scripts/seed-data.sh" $FORCE_FLAG

echo
echo "✅ Data seeding completed!"
echo
echo "📋 Next steps:"
echo "   1. Start your SAM backend: ./start.sh"
echo "   2. Test the API endpoints at http://localhost:3000"
echo "   3. Check the seeded data in your Flutter app"
echo
echo "🔑 Test user credentials:"
echo "   📧 <EMAIL> / DevPassword123!"
echo "   👑 <EMAIL> / AdminPassword123!"
echo "   👤 <EMAIL> / JohnPassword123!"
echo "   👤 <EMAIL> / JanePassword123!"
echo "   👤 <EMAIL> / MikePassword123!"
echo
