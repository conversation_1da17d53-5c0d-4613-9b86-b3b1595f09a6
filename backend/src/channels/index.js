const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');

// Initialize DynamoDB client
const dynamodb = new AWS.DynamoDB.DocumentClient();

// Environment variables
const CHANNELS_TABLE = process.env.CHANNELS_TABLE;
const CHANNEL_MEMBERS_TABLE = process.env.CHANNEL_MEMBERS_TABLE;
const USERS_TABLE = process.env.USERS_TABLE;
const POSTS_TABLE = process.env.POSTS_TABLE;
const MEDIA_TABLE = process.env.MEDIA_TABLE;

// CORS headers
const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
};

// Helper function to create response
const createResponse = (statusCode, body) => ({
    statusCode,
    headers: corsHeaders,
    body: JSON.stringify(body)
});

// Helper function to get user from context
const getUserFromContext = (event) => {
    // When using Lambda authorizer, user info is available in the context
    if (event.requestContext && event.requestContext.authorizer) {
        return {
            userId: event.requestContext.authorizer.userId,
            email: event.requestContext.authorizer.email || null,
            username: event.requestContext.authorizer.username || null
        };
    }
    throw new Error('User not authenticated');
};

// Get all channels with pagination
const getChannels = async (event) => {
    try {
        const { limit = 20, offset = 0 } = event.queryStringParameters || {};
        const user = getUserFromContext(event);

        console.log(`Getting channels - limit: ${limit}, offset: ${offset}`);

        // Scan channels table (in production, consider using a GSI for better performance)
        const params = {
            TableName: CHANNELS_TABLE,
            Limit: parseInt(limit),
            ExclusiveStartKey: offset ? JSON.parse(Buffer.from(offset, 'base64').toString()) : undefined
        };

        const result = await dynamodb.scan(params).promise();

        // For each channel, check if user is a member and get owner info
        const channelsWithMembership = await Promise.all(
            result.Items.map(async (channel) => {
                // Check if user is a member
                const membershipParams = {
                    TableName: CHANNEL_MEMBERS_TABLE,
                    Key: {
                        channel_id: channel.id,
                        user_id: user.userId
                    }
                };

                const membershipResult = await dynamodb.get(membershipParams).promise();
                const isUserMember = !!membershipResult.Item;
                const userRole = membershipResult.Item?.role || null;

                // Get owner information
                const ownerParams = {
                    TableName: USERS_TABLE,
                    Key: { id: channel.owner_id }
                };

                const ownerResult = await dynamodb.get(ownerParams).promise();
                const owner = ownerResult.Item;

                // Get channel icon from media table if icon_media_id exists
                let iconUrl = null;
                if (channel.icon_media_id) {
                    const iconParams = {
                        TableName: MEDIA_TABLE,
                        Key: { id: channel.icon_media_id }
                    };

                    const iconResult = await dynamodb.get(iconParams).promise();
                    iconUrl = iconResult.Item?.url || null;
                }

                return {
                    ...channel,
                    owner_username: owner?.username,
                    owner_display_name: owner?.display_name || `${owner?.firstName || ''} ${owner?.lastName || ''}`.trim(),
                    owner_avatar_url: owner?.avatar_url,
                    icon_url: iconUrl,
                    is_user_member: isUserMember,
                    user_role: userRole
                };
            })
        );

        const nextOffset = result.LastEvaluatedKey
            ? Buffer.from(JSON.stringify(result.LastEvaluatedKey)).toString('base64')
            : null;

        return createResponse(200, {
            channels: channelsWithMembership,
            hasMore: !!result.LastEvaluatedKey,
            nextOffset
        });

    } catch (error) {
        console.error('Error getting channels:', error);
        return createResponse(500, {
            error: 'Failed to get channels',
            details: error.message
        });
    }
};

// Create a new channel
const createChannel = async (event) => {
    try {
        const user = getUserFromContext(event);
        const body = JSON.parse(event.body);

        const { name, description, isPublic = true } = body;

        if (!name || name.trim().length === 0) {
            return createResponse(400, { error: 'Channel name is required' });
        }

        const channelId = uuidv4();
        const now = new Date().toISOString();

        // Create channel
        const channelParams = {
            TableName: CHANNELS_TABLE,
            Item: {
                id: channelId,
                name: name.trim(),
                description: description?.trim() || null,
                owner_id: user.userId,
                is_public: isPublic,
                is_active: true,
                member_count: 1, // Owner is automatically a member
                created_at: now,
                updated_at: now
            }
        };

        await dynamodb.put(channelParams).promise();

        // Add owner as a member with 'owner' role
        const memberParams = {
            TableName: CHANNEL_MEMBERS_TABLE,
            Item: {
                channel_id: channelId,
                user_id: user.userId,
                role: 'owner',
                joined_at: now
            }
        };

        await dynamodb.put(memberParams).promise();

        console.log(`Channel created: ${channelId} by user: ${user.userId}`);

        return createResponse(201, {
            id: channelId,
            name: name.trim(),
            description: description?.trim() || null,
            owner_id: user.userId,
            is_public: isPublic,
            is_active: true,
            member_count: 1,
            created_at: now,
            updated_at: now,
            is_user_member: true,
            user_role: 'owner'
        });

    } catch (error) {
        console.error('Error creating channel:', error);
        return createResponse(500, {
            error: 'Failed to create channel',
            details: error.message
        });
    }
};

// Get a specific channel
const getChannel = async (event) => {
    try {
        const channelId = event.pathParameters.id;
        const user = getUserFromContext(event);

        const params = {
            TableName: CHANNELS_TABLE,
            Key: { id: channelId }
        };

        const result = await dynamodb.get(params).promise();

        if (!result.Item) {
            return createResponse(404, { error: 'Channel not found' });
        }

        const channel = result.Item;

        // Check if user is a member
        const membershipParams = {
            TableName: CHANNEL_MEMBERS_TABLE,
            Key: {
                channel_id: channelId,
                user_id: user.userId
            }
        };

        const membershipResult = await dynamodb.get(membershipParams).promise();
        const isUserMember = !!membershipResult.Item;
        const userRole = membershipResult.Item?.role || null;

        // Get owner information
        const ownerParams = {
            TableName: USERS_TABLE,
            Key: { id: channel.owner_id }
        };

        const ownerResult = await dynamodb.get(ownerParams).promise();
        const owner = ownerResult.Item;

        // Get channel icon from media table if icon_media_id exists
        let iconUrl = null;
        if (channel.icon_media_id) {
            const iconParams = {
                TableName: MEDIA_TABLE,
                Key: { id: channel.icon_media_id }
            };

            const iconResult = await dynamodb.get(iconParams).promise();
            iconUrl = iconResult.Item?.url || null;
        }

        return createResponse(200, {
            ...channel,
            owner_username: owner?.username,
            owner_display_name: owner?.display_name || `${owner?.firstName || ''} ${owner?.lastName || ''}`.trim(),
            owner_avatar_url: owner?.avatar_url,
            icon_url: iconUrl,
            is_user_member: isUserMember,
            user_role: userRole
        });

    } catch (error) {
        console.error('Error getting channel:', error);
        return createResponse(500, {
            error: 'Failed to get channel',
            details: error.message
        });
    }
};

// Join a channel
const joinChannel = async (event) => {
    try {
        const channelId = event.pathParameters.id;
        const user = getUserFromContext(event);

        // Check if channel exists and is public
        const channelParams = {
            TableName: CHANNELS_TABLE,
            Key: { id: channelId }
        };

        const channelResult = await dynamodb.get(channelParams).promise();

        if (!channelResult.Item) {
            return createResponse(404, { error: 'Channel not found' });
        }

        const channel = channelResult.Item;

        if (!channel.is_public) {
            return createResponse(403, { error: 'Cannot join private channel' });
        }

        // Check if user is already a member
        const membershipParams = {
            TableName: CHANNEL_MEMBERS_TABLE,
            Key: {
                channel_id: channelId,
                user_id: user.userId
            }
        };

        const membershipResult = await dynamodb.get(membershipParams).promise();

        if (membershipResult.Item) {
            return createResponse(400, { error: 'Already a member of this channel' });
        }

        // Add user as member
        const now = new Date().toISOString();
        const addMemberParams = {
            TableName: CHANNEL_MEMBERS_TABLE,
            Item: {
                channel_id: channelId,
                user_id: user.userId,
                role: 'member',
                joined_at: now
            }
        };

        await dynamodb.put(addMemberParams).promise();

        // Update member count
        const updateChannelParams = {
            TableName: CHANNELS_TABLE,
            Key: { id: channelId },
            UpdateExpression: 'ADD member_count :inc SET updated_at = :now',
            ExpressionAttributeValues: {
                ':inc': 1,
                ':now': now
            }
        };

        await dynamodb.update(updateChannelParams).promise();

        console.log(`User ${user.userId} joined channel ${channelId}`);

        return createResponse(200, {
            message: 'Successfully joined channel',
            channel_id: channelId,
            user_role: 'member'
        });

    } catch (error) {
        console.error('Error joining channel:', error);
        return createResponse(500, {
            error: 'Failed to join channel',
            details: error.message
        });
    }
};

// Leave a channel
const leaveChannel = async (event) => {
    try {
        const channelId = event.pathParameters.id;
        const user = getUserFromContext(event);

        // Check if user is a member
        const membershipParams = {
            TableName: CHANNEL_MEMBERS_TABLE,
            Key: {
                channel_id: channelId,
                user_id: user.userId
            }
        };

        const membershipResult = await dynamodb.get(membershipParams).promise();

        if (!membershipResult.Item) {
            return createResponse(400, { error: 'Not a member of this channel' });
        }

        // Check if user is the owner
        if (membershipResult.Item.role === 'owner') {
            return createResponse(400, { error: 'Channel owner cannot leave channel' });
        }

        // Remove membership
        await dynamodb.delete(membershipParams).promise();

        // Update member count
        const now = new Date().toISOString();
        const updateChannelParams = {
            TableName: CHANNELS_TABLE,
            Key: { id: channelId },
            UpdateExpression: 'ADD member_count :dec SET updated_at = :now',
            ExpressionAttributeValues: {
                ':dec': -1,
                ':now': now
            }
        };

        await dynamodb.update(updateChannelParams).promise();

        console.log(`User ${user.userId} left channel ${channelId}`);

        return createResponse(200, {
            message: 'Successfully left channel',
            channel_id: channelId
        });

    } catch (error) {
        console.error('Error leaving channel:', error);
        return createResponse(500, {
            error: 'Failed to leave channel',
            details: error.message
        });
    }
};

// Main handler
exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    // Handle CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }

    try {
        const { httpMethod, resource } = event;

        switch (httpMethod) {
            case 'GET':
                if (resource === '/channels') {
                    return await getChannels(event);
                } else if (resource === '/channels/{id}') {
                    return await getChannel(event);
                } else if (resource === '/channels/{id}/posts') {
                    // TODO: Implement getChannelPosts
                    return createResponse(501, { error: 'Channel posts not implemented yet' });
                }
                break;

            case 'POST':
                if (resource === '/channels') {
                    return await createChannel(event);
                } else if (resource === '/channels/{id}/join') {
                    return await joinChannel(event);
                } else if (resource === '/channels/{id}/leave') {
                    return await leaveChannel(event);
                }
                break;

            case 'PUT':
                if (resource === '/channels/{id}') {
                    // TODO: Implement updateChannel
                    return createResponse(501, { error: 'Update channel not implemented yet' });
                }
                break;

            case 'DELETE':
                if (resource === '/channels/{id}') {
                    // TODO: Implement deleteChannel
                    return createResponse(501, { error: 'Delete channel not implemented yet' });
                }
                break;

            default:
                return createResponse(405, { error: 'Method not allowed' });
        }

        return createResponse(404, { error: 'Endpoint not found' });

    } catch (error) {
        console.error('Handler error:', error);
        return createResponse(500, {
            error: 'Internal server error',
            details: error.message
        });
    }
};
