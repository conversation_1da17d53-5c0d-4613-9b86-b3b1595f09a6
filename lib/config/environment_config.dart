import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Environment-specific configuration for GameFlex
/// This file contains URL configurations for different environments
class EnvironmentConfig {
  // SAM configuration (for development)
  static const String localhostApiPort = '3000';  // SAM API Gateway port
  static const String localhostS3Port = '3000';   // SAM S3 port (not used with R2)
  
  // Future production/staging subdomain configuration
  static const String stagingApiDomain = 'staging.api.gameflex.io';
  static const String stagingS3Domain = 'staging.media.gameflex.io';
  static const String productionApiDomain = 'api.gameflex.io';
  static const String productionS3Domain = 'media.gameflex.io';
  
  // Environment detection
  static const bool _isDevelopment = bool.fromEnvironment('dart.vm.product') == false;
  static const bool _isStaging = bool.fromEnvironment('STAGING', defaultValue: false);
  static const bool _isProduction = bool.fromEnvironment('PRODUCTION', defaultValue: false);
  
  /// Get the current environment
  static String get currentEnvironment {
    if (_isProduction) return 'production';
    if (_isStaging) return 'staging';
    if (_isDevelopment) return 'development';
    return 'unknown';
  }
  
  /// Check if running in development mode
  static bool get isDevelopment => _isDevelopment && !_isStaging && !_isProduction;
  
  /// Check if running in staging mode
  static bool get isStaging => _isStaging;
  
  /// Check if running in production mode
  static bool get isProduction => _isProduction;
  
  /// Get API base URLs for different environments
  static Map<String, String> get apiUrls => {
    'localhost': 'http://127.0.0.1:$localhostApiPort',
    'android_emulator': 'http://********:$localhostApiPort',
    'staging': 'https://$stagingApiDomain',
    'production': 'https://$productionApiDomain',
  };
  
  /// Get S3 base URLs for different environments
  static Map<String, String> get s3Urls => {
    'localhost': r2PublicUrl,
    'android_emulator': r2PublicUrl,
    'staging': 'https://$stagingS3Domain',
    'production': 'https://$productionS3Domain',
  };

  /// Get R2 public URL from environment variables
  static String get r2PublicUrl {
    return dotenv.env['R2_PUBLIC_URL'] ?? 'https://pub-34709f09e8384ef1a67928492571c01d.r2.dev';
  }
  
  /// Get debug information about current configuration
  static Map<String, dynamic> get debugInfo => {
    'environment': currentEnvironment,
    'isDevelopment': isDevelopment,
    'isStaging': isStaging,
    'isProduction': isProduction,
    'apiUrls': apiUrls,
    's3Urls': s3Urls,
  };
}
