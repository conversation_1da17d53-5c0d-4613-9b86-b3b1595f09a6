import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:gameflex_mobile/screens/splash_manager.dart';
import 'package:gameflex_mobile/theme/app_theme.dart';
import 'package:gameflex_mobile/providers/auth_provider.dart';
import 'package:gameflex_mobile/providers/posts_provider.dart';
import 'package:gameflex_mobile/providers/channels_provider.dart';
// UserProfile providers removed for AWS backend migration
import 'package:provider/provider.dart';
import 'package:window_manager/window_manager.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Load environment variables
  await dotenv.load(fileName: ".env");

  // Initialize AWS services (singleton instances will be created on first access)
  // ConfigService and AwsAuthService use lazy initialization

  // Set fixed window size for desktop platforms
  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
    await windowManager.ensureInitialized();

    WindowOptions windowOptions = WindowOptions(
      size: const Size(
        540,
        960,
      ), // FHD phone resolution (1080 x 1920) scaled down by 0.5
      center: true,
      backgroundColor: Colors.transparent,
      skipTaskbar: false,
      titleBarStyle: TitleBarStyle.normal,
      title: 'GameFlex',
      minimumSize: const Size(540, 960),
      maximumSize: const Size(540, 960),
    );

    await windowManager.waitUntilReadyToShow(windowOptions, () async {
      await windowManager.show();
      await windowManager.focus();
    });
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // Global route observer for video player lifecycle management
  static final RouteObserver<PageRoute> routeObserver =
      RouteObserver<PageRoute>();

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => AuthProvider()),
        ChangeNotifierProvider(create: (context) => PostsProvider()),
        ChangeNotifierProvider(create: (context) => ChannelsProvider()),
        // UserProfileProvider is created locally in UserProfileScreen for AWS backend
      ],
      child: MaterialApp(
        title: 'GameFlex',
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.dark,
        navigatorObservers: [routeObserver],
        home: const SplashManager(),
      ),
    );
  }
}
