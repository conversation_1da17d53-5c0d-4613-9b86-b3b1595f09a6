import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'api_service.dart';

/// AWS Cognito authentication service
class AwsAuthService {
  static AwsAuthService? _instance;
  static AwsAuthService get instance => _instance ??= AwsAuthService._();

  AwsAuthService._();

  // Storage keys for JWT tokens
  static const String _accessTokenKey = 'aws_access_token';
  static const String _refreshTokenKey = 'aws_refresh_token';
  static const String _idTokenKey = 'aws_id_token';
  static const String _userDataKey = 'aws_user_data';

  // Auth state stream
  final StreamController<AuthState> _authStateController = StreamController<AuthState>.broadcast();
  Stream<AuthState> get authStateChanges => _authStateController.stream;

  String? _accessToken;
  String? _refreshToken;
  String? _idToken;
  AwsUser? _currentUser;

  /// Check if user is currently authenticated
  bool get isAuthenticated => _accessToken != null && _currentUser != null;

  /// Get current user
  AwsUser? get currentUser => _currentUser;

  /// Get current access token
  String? get accessToken => _accessToken;

  /// Sign up with email and password
  Future<AwsAuthResponse> signUp({
    required String email,
    required String password,
    required String username,
  }) async {
    try {
      developer.log('AwsAuthService: Starting sign up for email: $email');
      if (kDebugMode) {
        print('AwsAuthService: Starting sign up for email: $email');
      }

      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/auth/signup',
        body: {
          'email': email,
          'password': password,
          'username': username,
        },
      );

      final data = ApiService.instance.parseResponse(response);

      developer.log('AwsAuthService: Sign up successful');
      if (kDebugMode) {
        print('AwsAuthService: Sign up successful');
      }

      return AwsAuthResponse(
        success: true,
        message: data['message'] as String? ?? 'User created successfully',
        user: data['user'] != null ? AwsUser.fromJson(data['user']) : null,
      );
    } catch (e) {
      developer.log('AwsAuthService: Sign up failed: $e');
      if (kDebugMode) {
        print('AwsAuthService: Sign up failed: $e');
      }
      
      return AwsAuthResponse(
        success: false,
        message: e.toString(),
      );
    }
  }

  /// Sign in with email and password
  Future<AwsAuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    try {
      developer.log('AwsAuthService: Starting sign in for email: $email');
      if (kDebugMode) {
        print('AwsAuthService: Starting sign in for email: $email');
      }

      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/auth/signin',
        body: {
          'email': email,
          'password': password,
        },
      );

      final data = ApiService.instance.parseResponse(response);

      // Extract tokens
      final tokens = data['tokens'] as Map<String, dynamic>?;
      if (tokens != null) {
        _accessToken = tokens['accessToken'] as String?;
        _refreshToken = tokens['refreshToken'] as String?;
        _idToken = tokens['idToken'] as String?;
      }

      // Extract user data
      final userData = data['user'] as Map<String, dynamic>?;
      if (userData != null) {
        _currentUser = AwsUser.fromJson(userData);
      }

      // Store tokens and user data
      await _storeAuthData();

      developer.log('AwsAuthService: Sign in successful');
      if (kDebugMode) {
        print('AwsAuthService: Sign in successful');
      }

      // Notify listeners
      _authStateController.add(AuthState.signedIn);

      return AwsAuthResponse(
        success: true,
        message: data['message'] as String? ?? 'Authentication successful',
        user: _currentUser,
        tokens: tokens,
      );
    } catch (e) {
      developer.log('AwsAuthService: Sign in failed: $e');
      if (kDebugMode) {
        print('AwsAuthService: Sign in failed: $e');
      }
      
      return AwsAuthResponse(
        success: false,
        message: e.toString(),
      );
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      if (_accessToken != null) {
        // Call backend signout endpoint
        await ApiService.instance.makeRequest(
          method: 'POST',
          path: '/auth/signout',
          accessToken: _accessToken,
        );
      }
    } catch (e) {
      developer.log('AwsAuthService: Backend signout failed: $e');
    }

    // Clear local data regardless of backend call result
    await _clearAuthData();
    
    // Notify listeners
    _authStateController.add(AuthState.signedOut);
    
    developer.log('AwsAuthService: Sign out completed');
  }

  /// Restore session from stored tokens
  Future<bool> restoreSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      _accessToken = prefs.getString(_accessTokenKey);
      _refreshToken = prefs.getString(_refreshTokenKey);
      _idToken = prefs.getString(_idTokenKey);

      final userDataJson = prefs.getString(_userDataKey);
      if (userDataJson != null) {
        final userData = json.decode(userDataJson) as Map<String, dynamic>;
        _currentUser = AwsUser.fromJson(userData);
      }

      if (_accessToken != null && _currentUser != null) {
        // Validate the token with the backend
        final isValid = await validateToken();
        if (isValid) {
          developer.log('AwsAuthService: Session restored and validated successfully');
          _authStateController.add(AuthState.signedIn);
          return true;
        } else {
          developer.log('AwsAuthService: Stored token is invalid, clearing session');
          await _clearAuthData();
          return false;
        }
      } else {
        developer.log('AwsAuthService: No valid session found');
        return false;
      }
    } catch (e) {
      developer.log('AwsAuthService: Failed to restore session: $e');
      await _clearAuthData();
      return false;
    }
  }

  /// Validate current access token with backend
  Future<bool> validateToken() async {
    if (_accessToken == null) {
      developer.log('AwsAuthService: No access token to validate');
      return false;
    }

    try {
      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/auth/validate',
        accessToken: _accessToken,
      );

      final data = ApiService.instance.parseResponse(response);

      if (data['valid'] == true) {
        // Update user data if provided
        final userData = data['user'] as Map<String, dynamic>?;
        if (userData != null) {
          _currentUser = AwsUser.fromJson(userData);
          await _storeAuthData(); // Update stored user data
        }

        developer.log('AwsAuthService: Token validation successful');
        return true;
      } else {
        developer.log('AwsAuthService: Token validation failed - response indicates invalid');
        return false;
      }
    } catch (e) {
      developer.log('AwsAuthService: Token validation error: $e');
      // Check if it's a 401 (unauthorized) or 403 (forbidden) - these indicate invalid token
      if (e.toString().contains('401') || e.toString().contains('403')) {
        developer.log('AwsAuthService: Token is definitely invalid (401/403)');
        return false;
      }
      // For other errors (network, 404, 500, etc.), assume token might still be valid
      developer.log('AwsAuthService: Token validation failed due to other error, assuming token is still valid');
      return true;
    }
  }

  /// Refresh access token
  Future<bool> refreshToken() async {
    if (_refreshToken == null) {
      developer.log('AwsAuthService: No refresh token available');
      return false;
    }

    try {
      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/auth/refresh',
        body: {
          'refreshToken': _refreshToken,
        },
      );

      final data = ApiService.instance.parseResponse(response);
      final tokens = data['tokens'] as Map<String, dynamic>?;
      
      if (tokens != null) {
        _accessToken = tokens['accessToken'] as String?;
        // Note: refresh token might be rotated
        if (tokens['refreshToken'] != null) {
          _refreshToken = tokens['refreshToken'] as String?;
        }
        if (tokens['idToken'] != null) {
          _idToken = tokens['idToken'] as String?;
        }
        
        await _storeAuthData();
        developer.log('AwsAuthService: Token refresh successful');
        return true;
      }
      
      return false;
    } catch (e) {
      developer.log('AwsAuthService: Token refresh failed: $e');
      await _clearAuthData();
      _authStateController.add(AuthState.signedOut);
      return false;
    }
  }

  /// Store authentication data
  Future<void> _storeAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    
    if (_accessToken != null) {
      await prefs.setString(_accessTokenKey, _accessToken!);
    }
    if (_refreshToken != null) {
      await prefs.setString(_refreshTokenKey, _refreshToken!);
    }
    if (_idToken != null) {
      await prefs.setString(_idTokenKey, _idToken!);
    }
    if (_currentUser != null) {
      await prefs.setString(_userDataKey, json.encode(_currentUser!.toJson()));
    }
  }

  /// Clear authentication data
  Future<void> _clearAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.remove(_accessTokenKey);
    await prefs.remove(_refreshTokenKey);
    await prefs.remove(_idTokenKey);
    await prefs.remove(_userDataKey);
    
    _accessToken = null;
    _refreshToken = null;
    _idToken = null;
    _currentUser = null;
  }

  /// Dispose resources
  void dispose() {
    _authStateController.close();
  }
}

/// Authentication state enum
enum AuthState {
  signedIn,
  signedOut,
}

/// AWS authentication response
class AwsAuthResponse {
  final bool success;
  final String message;
  final AwsUser? user;
  final Map<String, dynamic>? tokens;

  AwsAuthResponse({
    required this.success,
    required this.message,
    this.user,
    this.tokens,
  });
}

/// AWS user model
class AwsUser {
  final String id;
  final String email;
  final String username;
  final String? displayName;
  final String? avatarUrl;
  final bool isVerified;

  AwsUser({
    required this.id,
    required this.email,
    required this.username,
    this.displayName,
    this.avatarUrl,
    this.isVerified = false,
  });

  factory AwsUser.fromJson(Map<String, dynamic> json) {
    return AwsUser(
      id: json['id'] as String,
      email: json['email'] as String,
      username: json['username'] as String,
      displayName: json['display_name'] as String?,
      avatarUrl: json['avatar_url'] as String?,
      isVerified: json['is_verified'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'username': username,
      'display_name': displayName,
      'avatar_url': avatarUrl,
      'is_verified': isVerified,
    };
  }
}
