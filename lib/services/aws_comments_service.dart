import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import '../models/comment_model.dart';
import 'api_service.dart';
import 'aws_auth_service.dart';

class AwsCommentsService {
  static final AwsCommentsService _instance = AwsCommentsService._internal();
  static AwsCommentsService get instance => _instance;
  AwsCommentsService._internal();

  /// Get comments for a specific post
  Future<List<CommentModel>> getComments(String postId) async {
    try {
      developer.log('AwsCommentsService: Getting comments for post $postId');

      final accessToken = AwsAuthService.instance.accessToken;
      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/posts/$postId/comments',
        accessToken: accessToken,
      );

      final data = ApiService.instance.parseResponse(response);
      final List<dynamic> commentsJson = data['comments'] ?? [];

      final comments = commentsJson
          .map((json) => CommentModel.fromJson(json as Map<String, dynamic>))
          .toList();

      developer.log('AwsCommentsService: Retrieved ${comments.length} comments');
      return comments;
    } catch (e) {
      developer.log('AwsCommentsService: Error getting comments: $e');
      if (kDebugMode) {
        print('AwsCommentsService ERROR: $e');
      }
      rethrow;
    }
  }

  /// Create a new comment on a post
  Future<CommentModel?> createComment({
    required String postId,
    required String content,
  }) async {
    try {
      developer.log('AwsCommentsService: Creating comment on post $postId');

      final accessToken = AwsAuthService.instance.accessToken;
      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/posts/$postId/comments',
        body: {'content': content},
        accessToken: accessToken,
      );

      final data = ApiService.instance.parseResponse(response);
      final commentJson = data['comment'];

      if (commentJson != null) {
        final comment = CommentModel.fromJson(commentJson as Map<String, dynamic>);
        developer.log('AwsCommentsService: Created comment ${comment.id}');
        return comment;
      }

      return null;
    } catch (e) {
      developer.log('AwsCommentsService: Error creating comment: $e');
      if (kDebugMode) {
        print('AwsCommentsService ERROR: $e');
      }
      rethrow;
    }
  }

  /// Update an existing comment
  Future<CommentModel?> updateComment({
    required String commentId,
    required String content,
  }) async {
    try {
      developer.log('AwsCommentsService: Updating comment $commentId');

      final accessToken = AwsAuthService.instance.accessToken;
      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final response = await ApiService.instance.makeRequest(
        method: 'PUT',
        path: '/comments/$commentId',
        body: {'content': content},
        accessToken: accessToken,
      );

      final data = ApiService.instance.parseResponse(response);
      final commentJson = data['comment'];

      if (commentJson != null) {
        final comment = CommentModel.fromJson(commentJson as Map<String, dynamic>);
        developer.log('AwsCommentsService: Updated comment ${comment.id}');
        return comment;
      }

      return null;
    } catch (e) {
      developer.log('AwsCommentsService: Error updating comment: $e');
      if (kDebugMode) {
        print('AwsCommentsService ERROR: $e');
      }
      rethrow;
    }
  }

  /// Delete a comment
  Future<bool> deleteComment(String commentId) async {
    try {
      developer.log('AwsCommentsService: Deleting comment $commentId');

      final accessToken = AwsAuthService.instance.accessToken;
      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final response = await ApiService.instance.makeRequest(
        method: 'DELETE',
        path: '/comments/$commentId',
        accessToken: accessToken,
      );

      ApiService.instance.parseResponse(response);
      developer.log('AwsCommentsService: Deleted comment $commentId');
      return true;
    } catch (e) {
      developer.log('AwsCommentsService: Error deleting comment: $e');
      if (kDebugMode) {
        print('AwsCommentsService ERROR: $e');
      }
      return false;
    }
  }
}
